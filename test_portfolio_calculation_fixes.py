#!/usr/bin/env python3
"""
Test script to verify portfolio calculation fixes.

This script tests the fixes for:
1. Currency conversion rate inconsistency
2. Individual stock values vs total portfolio value mismatch
3. P&L percentage changes when switching currencies
"""

import sys
import os

# CENTRALIZED CURRENCY CONVERSION RATES (copied from portfolio_service.py)
CURRENCY_CONVERSION_RATES = {
    'USD': 1.0,
    'DKK': 6.9,    # 1 USD = 6.9 DKK
    'EUR': 0.926,  # 1 USD = 0.926 EUR
    'GBP': 0.787,  # 1 USD = 0.787 GBP
    'SEK': 10.87,  # 1 USD = 10.87 SEK
    'NOK': 10.99,  # 1 USD = 10.99 NOK
    'JPY': 149.0,  # 1 USD = 149 JPY
    'CAD': 1.36,   # 1 USD = 1.36 CAD
    'AUD': 1.52,   # 1 USD = 1.52 AUD
    'CHF': 0.88,   # 1 USD = 0.88 CHF
    'PLN': 4.02,   # 1 USD = 4.02 PLN
    'CZK': 22.8,   # 1 USD = 22.8 CZK
    'HUF': 360.0,  # 1 USD = 360 HUF
    'BRL': 5.15,   # 1 USD = 5.15 BRL
    'MXN': 17.2,   # 1 USD = 17.2 MXN
    'INR': 83.1,   # 1 USD = 83.1 INR
    'KRW': 1320.0, # 1 USD = 1320 KRW
    'SGD': 1.35,   # 1 USD = 1.35 SGD
    'HKD': 7.8,    # 1 USD = 7.8 HKD
    'NZD': 1.62,   # 1 USD = 1.62 NZD
    'ZAR': 18.5,   # 1 USD = 18.5 ZAR
    'RUB': 92.0,   # 1 USD = 92 RUB
    'TRY': 28.5,   # 1 USD = 28.5 TRY
    'THB': 35.2,   # 1 USD = 35.2 THB
    'MYR': 4.65,   # 1 USD = 4.65 MYR
    'IDR': 15600.0, # 1 USD = 15600 IDR
    'PHP': 56.0,   # 1 USD = 56 PHP
    'ILS': 3.7,    # 1 USD = 3.7 ILS
    'VND': 24300.0, # 1 USD = 24300 VND
    'CNY': 7.25,   # 1 USD = 7.25 CNY
}

def convert_currency(amount, from_currency, to_currency):
    """
    Convert amount from one currency to another using centralized rates.
    """
    if from_currency == to_currency:
        return amount

    # Convert to USD first if not already USD
    if from_currency != 'USD':
        usd_rate = CURRENCY_CONVERSION_RATES.get(from_currency, 1.0)
        amount_usd = amount / usd_rate
    else:
        amount_usd = amount

    # Convert from USD to target currency
    target_rate = CURRENCY_CONVERSION_RATES.get(to_currency, 1.0)
    return amount_usd * target_rate

def test_currency_conversion_consistency():
    """Test that currency conversion rates are consistent."""
    print("🧪 Testing Currency Conversion Consistency...")
    
    # Test USD to PHP conversion (user's currency)
    usd_amount = 1000.0
    php_amount = convert_currency(usd_amount, 'USD', 'PHP')
    expected_php = usd_amount * CURRENCY_CONVERSION_RATES['PHP']  # 1000 * 56 = 56000
    
    print(f"   USD to PHP: ${usd_amount} → ₱{php_amount}")
    print(f"   Expected: ₱{expected_php}")
    
    if abs(php_amount - expected_php) < 0.01:
        print("   ✅ Currency conversion consistent")
    else:
        print("   ❌ Currency conversion inconsistent")
        return False
    
    # Test round-trip conversion
    back_to_usd = convert_currency(php_amount, 'PHP', 'USD')
    print(f"   PHP to USD (round-trip): ₱{php_amount} → ${back_to_usd}")
    
    if abs(back_to_usd - usd_amount) < 0.01:
        print("   ✅ Round-trip conversion consistent")
    else:
        print("   ❌ Round-trip conversion inconsistent")
        return False
    
    return True

def test_single_stock_portfolio():
    """Test that individual stock value matches total portfolio value for single stock."""
    print("\n🧪 Testing Single Stock Portfolio Calculation...")

    # Simulate user's GOOG.US stock in PHP currency
    # Based on user's report: individual stock ₱1187.19, but total shows ₱65,954.86
    test_portfolio = [{
        'ticker': 'GOOG.US',
        'company': 'Alphabet Inc',
        'shares': 1.0,  # 1 share for simplicity
        'amount_invested': 1000.0,  # ₱1000 invested
        'current_value': 1187.19,   # Current value ₱1187.19
        'currency': 'PHP',
        'amount_invested_currency': 'PHP',
        'buy_price': 1000.0,
        'current_price': 21.2,  # USD price from API
        'gain_loss': 187.19,
        'gain_loss_percent': 18.719,
        'pure_gain': 187.19,
        'percent_gain': 18.719
    }]

    # Simulate portfolio calculation logic
    display_currency = 'PHP'
    total_invested_display = 0
    total_current_value_display = 0

    for stock in test_portfolio:
        currency = stock.get('amount_invested_currency', stock.get('currency', 'USD'))
        invested_amount = stock.get('amount_invested', 0.0)
        current_value = stock.get('current_value', 0.0)

        # Convert to display currency
        invested_display = convert_currency(invested_amount, currency, display_currency)
        current_val_display = convert_currency(current_value, currency, display_currency)

        total_invested_display += invested_display
        total_current_value_display += current_val_display

    print(f"   Individual stock current value: ₱{test_portfolio[0]['current_value']}")
    print(f"   Total portfolio stocks value: ₱{total_current_value_display}")

    # For a single stock portfolio, individual stock value should equal total stocks value
    individual_value = test_portfolio[0]['current_value']

    if abs(individual_value - total_current_value_display) < 0.01:
        print("   ✅ Individual stock value matches total stocks value")
        return True
    else:
        print(f"   ❌ Mismatch: Individual (₱{individual_value}) vs Total (₱{total_current_value_display})")
        return False

def test_pnl_percentage_preservation():
    """Test that P&L percentages don't change when switching currencies."""
    print("\n🧪 Testing P&L Percentage Preservation...")
    
    # Create test stock with known P&L percentage
    original_invested = 1000.0  # $1000 USD
    original_current = 1200.0   # $1200 USD
    expected_percentage = ((original_current - original_invested) / original_invested) * 100  # 20%
    
    test_stock = {
        'ticker': 'TEST.US',
        'amount_invested': original_invested,
        'current_value': original_current,
        'currency': 'USD',
        'gain_loss_percent': expected_percentage,
        'percent_gain': expected_percentage
    }
    
    print(f"   Original: ${original_invested} invested, ${original_current} current")
    print(f"   Original P&L: {expected_percentage:.2f}%")
    
    # Convert to PHP
    php_invested = convert_currency(original_invested, 'USD', 'PHP')
    php_current = convert_currency(original_current, 'USD', 'PHP')
    
    # Calculate P&L percentage in PHP (should be the same)
    php_percentage = ((php_current - php_invested) / php_invested) * 100
    
    print(f"   Converted: ₱{php_invested} invested, ₱{php_current} current")
    print(f"   PHP P&L: {php_percentage:.2f}%")
    
    if abs(php_percentage - expected_percentage) < 0.01:
        print("   ✅ P&L percentage preserved across currencies")
        return True
    else:
        print(f"   ❌ P&L percentage changed: {expected_percentage:.2f}% → {php_percentage:.2f}%")
        return False

def main():
    """Run all tests."""
    print("🚀 Portfolio Calculation Fixes Test Suite")
    print("=" * 50)
    
    tests = [
        test_currency_conversion_consistency,
        test_single_stock_portfolio,
        test_pnl_percentage_preservation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"   ❌ Test failed with error: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Portfolio calculation fixes are working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the fixes.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
