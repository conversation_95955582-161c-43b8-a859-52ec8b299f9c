#!/usr/bin/env python3
"""
Complete test of the currency switching fix to ensure P&L percentages are preserved.
This simulates the entire flow: API currency switch + portfolio calculation.
"""

import sys
import os

# CENTRALIZED CURRENCY CONVERSION RATES (from portfolio_service.py)
CURRENCY_CONVERSION_RATES = {
    'USD': 1.0,
    'DKK': 6.9,    # 1 USD = 6.9 DKK
    'EUR': 0.926,  # 1 USD = 0.926 EUR
    'GBP': 0.787,  # 1 USD = 0.787 GBP
    'PHP': 56.0,   # 1 USD = 56 PHP
}

def convert_currency(amount, from_currency, to_currency):
    """Convert amount from one currency to another using centralized rates."""
    if from_currency == to_currency:
        return amount
    
    # Convert to USD first if not already USD
    if from_currency != 'USD':
        usd_rate = CURRENCY_CONVERSION_RATES.get(from_currency, 1.0)
        amount_usd = amount / usd_rate
    else:
        amount_usd = amount
    
    # Convert from USD to target currency
    target_rate = CURRENCY_CONVERSION_RATES.get(to_currency, 1.0)
    return amount_usd * target_rate

def safe_float(value):
    """Safely convert value to float."""
    try:
        return float(value)
    except (ValueError, TypeError):
        return 0.0

def simulate_currency_switch_api(portfolio, from_currency, to_currency):
    """Simulate the /api/portfolio/currency endpoint with the fix."""
    print(f"🔄 Simulating API currency switch: {from_currency} → {to_currency}")
    
    # Update all portfolio entries (simulating the fixed API endpoint)
    for entry in portfolio:
        entry_currency = entry.get('currency', from_currency)
        
        if entry_currency != to_currency:
            # PRESERVE P&L PERCENTAGES - they should not change when switching display currencies
            original_gain_percent = safe_float(entry.get('gain_loss_percent', 0))
            original_percent_gain = safe_float(entry.get('percent_gain', 0))
            
            # Convert amounts using centralized currency conversion
            current_amount = safe_float(entry.get('amount_invested', 0))
            current_price = safe_float(entry.get('buy_price', 0))
            current_value = safe_float(entry.get('current_value', 0))
            pure_gain = safe_float(entry.get('pure_gain', 0))
            
            # Use centralized conversion function
            new_amount = convert_currency(current_amount, entry_currency, to_currency)
            new_price = convert_currency(current_price, entry_currency, to_currency)
            new_value = convert_currency(current_value, entry_currency, to_currency)
            new_gain = convert_currency(pure_gain, entry_currency, to_currency)
            
            # Update entry
            entry['amount_invested'] = new_amount
            entry['buy_price'] = new_price
            entry['current_value'] = new_value
            entry['pure_gain'] = new_gain
            entry['gain_loss'] = new_gain
            entry['currency'] = to_currency
            entry['amount_invested_currency'] = to_currency  # CRITICAL FIX: Update this too!

            # CRITICAL FIX: Preserve original P&L percentages
            entry['gain_loss_percent'] = original_gain_percent
            entry['percent_gain'] = original_percent_gain
            
            print(f"   {entry['ticker']}: Preserved P&L {original_gain_percent:.2f}%")

def calculate_portfolio_data_fixed(portfolio, cash_position, display_currency):
    """Fixed version of calculate_portfolio_data that preserves percentages."""

    # Convert all values to display currency for portfolio totals
    total_invested_display = 0
    total_current_value_display = 0

    # CRITICAL FIX: Calculate percentage in USD to ensure consistency across currency switches
    total_invested_usd = 0
    total_current_value_usd = 0

    for stock in portfolio:
        # Use amount_invested_currency if available, otherwise fall back to currency
        currency = stock.get('amount_invested_currency', stock.get('currency', 'USD'))

        # Get amounts in original currency
        invested_amount = safe_float(stock.get('amount_invested', 0.0))
        current_value = safe_float(stock.get('current_value', 0.0))

        # CRITICAL FIX: Ensure gain_loss_percent is calculated if not present
        if 'gain_loss_percent' not in stock or stock['gain_loss_percent'] is None:
            if invested_amount > 0.001:
                stock['gain_loss_percent'] = ((current_value - invested_amount) / invested_amount) * 100
            else:
                stock['gain_loss_percent'] = 0.0

        # Convert to display currency for totals
        invested_display = convert_currency(invested_amount, currency, display_currency)
        current_val_display = convert_currency(current_value, currency, display_currency)

        total_invested_display += invested_display
        total_current_value_display += current_val_display

        # CRITICAL FIX: Also convert to USD for consistent percentage calculation
        invested_usd = convert_currency(invested_amount, currency, 'USD')
        current_val_usd = convert_currency(current_value, currency, 'USD')

        total_invested_usd += invested_usd
        total_current_value_usd += current_val_usd

    # Cash position should also be converted to display currency if needed
    cash_display = convert_currency(cash_position, 'USD', display_currency)
    total_portfolio_value = total_current_value_display + cash_display

    # CRITICAL FIX: Calculate percentage using weighted average of individual stock percentages
    # Use USD amounts for consistent weighting across all currencies
    total_weight_usd = 0
    weighted_percentage_sum = 0

    for stock in portfolio:
        # Use amount_invested_currency if available, otherwise fall back to currency
        currency = stock.get('amount_invested_currency', stock.get('currency', 'USD'))
        invested_amount = safe_float(stock.get('amount_invested', 0.0))
        stock_percentage = safe_float(stock.get('gain_loss_percent', 0.0))

        # Convert investment amount to USD for consistent weighting
        invested_amount_usd = convert_currency(invested_amount, currency, 'USD')

        if invested_amount_usd > 0.001:  # Only include stocks with meaningful investment
            total_weight_usd += invested_amount_usd
            weighted_percentage_sum += (invested_amount_usd * stock_percentage)

    # Calculate weighted average percentage
    total_percent_gain = (weighted_percentage_sum / total_weight_usd) if total_weight_usd > 0.001 else 0
    
    # Display gain in the user's display currency
    total_pure_gain_display = total_current_value_display - total_invested_display

    summary = {
        'total_invested': total_invested_display,
        'total_current_value_stocks': total_current_value_display,
        'cash_position': cash_display,
        'total_portfolio_value': total_portfolio_value,
        'total_pure_gain': total_pure_gain_display,  # Display gain in user's currency
        'total_percent_gain': total_percent_gain,    # Percentage calculated consistently in USD
        'stock_count': len(portfolio)
    }
    return summary

def test_complete_currency_switch_flow():
    """Test the complete currency switching flow."""
    print("🧪 Testing Complete Currency Switch Flow")
    print("=" * 60)
    
    # Create test portfolio (simulating user's scenario)
    original_portfolio = [
        {
            'ticker': 'GOOG.US',
            'amount_invested': 1000.0,      # $1000 USD invested
            'current_value': 1200.0,        # $1200 USD current (20% gain)
            'currency': 'USD',
            'amount_invested_currency': 'USD',
            'buy_price': 100.0,
            'pure_gain': 200.0,
            'gain_loss': 200.0,
            'gain_loss_percent': 20.0,      # 20% gain
            'percent_gain': 20.0
        },
        {
            'ticker': 'NOVO.CO',
            'amount_invested': 6900.0,      # 6900 DKK invested
            'current_value': 7590.0,        # 7590 DKK current (10% gain)
            'currency': 'DKK',
            'amount_invested_currency': 'DKK',
            'buy_price': 690.0,
            'pure_gain': 690.0,
            'gain_loss': 690.0,
            'gain_loss_percent': 10.0,      # 10% gain
            'percent_gain': 10.0
        }
    ]
    
    cash_position = 0.0
    
    # Step 1: Calculate initial portfolio in USD
    print("1. Initial Portfolio (USD):")
    portfolio_usd = [stock.copy() for stock in original_portfolio]  # Deep copy
    summary_usd = calculate_portfolio_data_fixed(portfolio_usd, cash_position, 'USD')
    print(f"   Total Percentage: {summary_usd['total_percent_gain']:.2f}%")
    
    # Step 2: Simulate currency switch to DKK via API
    print("\n2. Currency Switch API Call (USD → DKK):")
    portfolio_dkk = [stock.copy() for stock in original_portfolio]  # Fresh copy
    simulate_currency_switch_api(portfolio_dkk, 'USD', 'DKK')
    
    # Step 3: Calculate portfolio after switch
    print("\n3. Portfolio After Switch (DKK):")
    summary_dkk = calculate_portfolio_data_fixed(portfolio_dkk, cash_position, 'DKK')
    print(f"   Total Percentage: {summary_dkk['total_percent_gain']:.2f}%")
    
    # Step 4: Switch to PHP
    print("\n4. Currency Switch API Call (DKK → PHP):")
    portfolio_php = [stock.copy() for stock in portfolio_dkk]  # From DKK state
    simulate_currency_switch_api(portfolio_php, 'DKK', 'PHP')
    
    print("\n5. Portfolio After Second Switch (PHP):")
    summary_php = calculate_portfolio_data_fixed(portfolio_php, cash_position, 'PHP')
    print(f"   Total Percentage: {summary_php['total_percent_gain']:.2f}%")
    
    # Verify consistency
    print("\n" + "=" * 60)
    print("📊 TOTAL PERCENTAGE CONSISTENCY CHECK:")
    print(f"   USD: {summary_usd['total_percent_gain']:.2f}%")
    print(f"   DKK: {summary_dkk['total_percent_gain']:.2f}%")
    print(f"   PHP: {summary_php['total_percent_gain']:.2f}%")
    
    usd_percent = summary_usd['total_percent_gain']
    dkk_percent = summary_dkk['total_percent_gain']
    php_percent = summary_php['total_percent_gain']
    
    if (abs(usd_percent - dkk_percent) < 0.01 and 
        abs(usd_percent - php_percent) < 0.01 and 
        abs(dkk_percent - php_percent) < 0.01):
        print("   ✅ SUCCESS: Total percentages are consistent across all currency switches!")
        return True
    else:
        print("   ❌ FAILURE: Total percentages are inconsistent!")
        print(f"      USD vs DKK diff: {abs(usd_percent - dkk_percent):.4f}%")
        print(f"      USD vs PHP diff: {abs(usd_percent - php_percent):.4f}%")
        print(f"      DKK vs PHP diff: {abs(dkk_percent - php_percent):.4f}%")
        return False

if __name__ == "__main__":
    success = test_complete_currency_switch_flow()
    if success:
        print("\n🎉 COMPLETE FIX VERIFIED!")
        print("The user's total gain percentage will no longer change when switching currencies.")
    else:
        print("\n⚠️  The fix needs more work.")
    
    sys.exit(0 if success else 1)
