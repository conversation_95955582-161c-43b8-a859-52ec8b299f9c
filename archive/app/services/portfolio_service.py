# -*- coding: utf-8 -*-
"""
Portfolio Service

This module contains all portfolio management business logic including
calculations, health scoring, and analysis functions.
"""

import threading
from concurrent.futures import ThreadPoolExecutor
from flask import current_app, session

from ..utils.formatters import safe_float
from .api_client import fetch_eodhd_realtime_price, fetch_eodhd_fundamentals
from .financial_calculator import calculate_comprehensive_financial_metrics


# Thread lock for portfolio calculations
calculation_lock = threading.Lock()

# Thread pool executor for parallel operations
executor = ThreadPoolExecutor(max_workers=10)

# CENTRALIZED CURRENCY CONVERSION RATES
# All rates are USD to target currency (1 USD = X target currency)
CURRENCY_CONVERSION_RATES = {
    'USD': 1.0,
    'DKK': 6.9,    # 1 USD = 6.9 DKK
    'EUR': 0.926,  # 1 USD = 0.926 EUR
    'GBP': 0.787,  # 1 USD = 0.787 GBP
    'SEK': 10.87,  # 1 USD = 10.87 SEK
    'NOK': 10.99,  # 1 USD = 10.99 NOK
    'JPY': 149.0,  # 1 USD = 149 JPY
    'CAD': 1.36,   # 1 USD = 1.36 CAD
    'AUD': 1.52,   # 1 USD = 1.52 AUD
    'CHF': 0.88,   # 1 USD = 0.88 CHF
    'PLN': 4.02,   # 1 USD = 4.02 PLN
    'CZK': 22.8,   # 1 USD = 22.8 CZK
    'HUF': 360.0,  # 1 USD = 360 HUF
    'BRL': 5.15,   # 1 USD = 5.15 BRL
    'MXN': 17.2,   # 1 USD = 17.2 MXN
    'INR': 83.1,   # 1 USD = 83.1 INR
    'KRW': 1320.0, # 1 USD = 1320 KRW
    'SGD': 1.35,   # 1 USD = 1.35 SGD
    'HKD': 7.8,    # 1 USD = 7.8 HKD
    'NZD': 1.62,   # 1 USD = 1.62 NZD
    'ZAR': 18.5,   # 1 USD = 18.5 ZAR
    'RUB': 92.0,   # 1 USD = 92 RUB
    'TRY': 28.5,   # 1 USD = 28.5 TRY
    'THB': 35.2,   # 1 USD = 35.2 THB
    'MYR': 4.65,   # 1 USD = 4.65 MYR
    'IDR': 15600.0, # 1 USD = 15600 IDR
    'PHP': 56.0,   # 1 USD = 56 PHP
    'ILS': 3.7,    # 1 USD = 3.7 ILS
    'VND': 24300.0, # 1 USD = 24300 VND
    'CNY': 7.25,   # 1 USD = 7.25 CNY
}

def convert_currency(amount, from_currency, to_currency):
    """
    Convert amount from one currency to another using centralized rates.

    Args:
        amount: Amount to convert
        from_currency: Source currency code (e.g., 'USD')
        to_currency: Target currency code (e.g., 'DKK')

    Returns:
        Converted amount
    """
    if from_currency == to_currency:
        return amount

    # Convert to USD first if not already USD
    if from_currency != 'USD':
        usd_rate = CURRENCY_CONVERSION_RATES.get(from_currency, 1.0)
        amount_usd = amount / usd_rate
    else:
        amount_usd = amount

    # Convert from USD to target currency
    target_rate = CURRENCY_CONVERSION_RATES.get(to_currency, 1.0)
    return amount_usd * target_rate


def fetch_portfolio_data():
    """Fetch portfolio data from session."""
    return session.get('portfolio_data', [])


def get_portfolio_summary():
    """Get portfolio summary with current values and metrics."""
    try:
        # Update portfolio values first
        update_portfolio_values()
        
        # Calculate summary data
        summary, cash_position = calculate_portfolio_data()
        
        return {
            'success': True,
            'summary': summary,
            'cash_position': cash_position,
            'portfolio': fetch_portfolio_data()
        }
    except Exception as e:
        current_app.logger.error(f"Error getting portfolio summary: {e}")
        return {'success': False, 'error': str(e)}


def calculate_portfolio_data():
    """Calculates summary metrics for the entire portfolio based on data in session."""
    with calculation_lock:
        portfolio = session.get('portfolio_data', [])
        cash_position = safe_float(session.get('cash_position', 0.0))

        # Get user's display currency from session
        display_currency = session.get('portfolio_currency', 'USD')

        # Convert all values to display currency for portfolio totals
        # CRITICAL FIX: Calculate totals in user's display currency, not always USD
        total_invested_display = 0
        total_current_value_display = 0

        # CRITICAL FIX: Calculate percentage in USD to ensure consistency across currency switches
        total_invested_usd = 0
        total_current_value_usd = 0

        for stock in portfolio:
            # Use amount_invested_currency if available, otherwise fall back to currency
            currency = stock.get('amount_invested_currency', stock.get('currency', 'USD'))

            # Get amounts in original currency
            invested_amount = safe_float(stock.get('amount_invested', 0.0))
            current_value = safe_float(stock.get('current_value', 0.0))

            # CRITICAL FIX: Ensure gain_loss_percent is calculated if not present
            if 'gain_loss_percent' not in stock or stock['gain_loss_percent'] is None:
                if invested_amount > 0.001:
                    stock['gain_loss_percent'] = ((current_value - invested_amount) / invested_amount) * 100
                else:
                    stock['gain_loss_percent'] = 0.0

            # Convert to display currency for totals
            invested_display = convert_currency(invested_amount, currency, display_currency)
            current_val_display = convert_currency(current_value, currency, display_currency)

            total_invested_display += invested_display
            total_current_value_display += current_val_display

            # CRITICAL FIX: Also convert to USD for consistent percentage calculation
            invested_usd = convert_currency(invested_amount, currency, 'USD')
            current_val_usd = convert_currency(current_value, currency, 'USD')

            total_invested_usd += invested_usd
            total_current_value_usd += current_val_usd

        # Cash position should also be converted to display currency if needed
        # Assuming cash is stored in USD by default
        cash_display = convert_currency(cash_position, 'USD', display_currency)
        total_portfolio_value = total_current_value_display + cash_display

        # CRITICAL FIX: Calculate percentage using weighted average of individual stock percentages
        # This ensures the total percentage is consistent regardless of currency conversions
        total_weight = 0
        weighted_percentage_sum = 0

        for stock in portfolio:
            invested_amount = safe_float(stock.get('amount_invested', 0.0))
            stock_percentage = safe_float(stock.get('gain_loss_percent', 0.0))

            if invested_amount > 0.001:  # Only include stocks with meaningful investment
                total_weight += invested_amount
                weighted_percentage_sum += (invested_amount * stock_percentage)

        # Calculate weighted average percentage
        total_percent_gain = (weighted_percentage_sum / total_weight) if total_weight > 0.001 else 0

        # Display gain in the user's display currency
        total_pure_gain_display = total_current_value_display - total_invested_display

        summary = {
            'total_invested': total_invested_display,
            'total_current_value_stocks': total_current_value_display,
            'cash_position': cash_display,
            'total_portfolio_value': total_portfolio_value,
            'total_pure_gain': total_pure_gain_display,  # Display gain in user's currency
            'total_percent_gain': total_percent_gain,    # Percentage calculated consistently in USD
            'stock_count': len(portfolio)
        }
        return summary, cash_display


def update_portfolio_values():
    """Updates the current value and gains for each stock in the portfolio using parallel API calls."""
    current_app.logger.info("Starting portfolio value update...")
    portfolio = fetch_portfolio_data()
    if not portfolio:
        current_app.logger.info("Portfolio empty, skipping value update.")
        return

    tickers = list({stock.get('ticker') for stock in portfolio if stock.get('ticker') != 'UNKNOWN'})

    if not tickers:
        current_app.logger.info("No valid tickers found in portfolio to update.")
        return

    # Fetch current prices in parallel
    price_cache = {}
    def fetch_price_task(ticker):
        return ticker, fetch_eodhd_realtime_price(ticker)

    try:
        results = executor.map(fetch_price_task, tickers)
        for ticker, price_data in results:
            price_float = safe_float(price_data.get('close'), None) if price_data else None
            price_cache[ticker] = price_float
            if price_cache[ticker] is None:
                current_app.logger.warning(f"Could not fetch valid price for {ticker} during update.")
    except Exception as e:
        current_app.logger.error(f"Error fetching prices in parallel for portfolio update: {e}", exc_info=True)

    # Update each stock's value in the portfolio list
    updated_count = 0
    needs_saving = False
    for stock in portfolio:
        ticker = stock.get('ticker')
        if ticker == 'UNKNOWN.' or ticker not in price_cache:
            continue

        current_price_local = price_cache.get(ticker)
        if current_price_local is None:
            current_app.logger.warning(f"Skipping value update for {ticker} due to missing price in this cycle.")
            continue

        stock_currency = stock.get('currency', 'USD')
        invested_original = safe_float(stock.get('amount_invested', 0.0))
        shares = safe_float(stock.get('shares', 0.0))

        # Only update if shares are valid
        if shares > 0:
            # Current price is typically in USD from API
            current_price_usd = current_price_local

            # Convert current value to the same currency as invested amount using centralized conversion
            current_price_local_currency = convert_currency(current_price_usd, 'USD', stock_currency)
            current_value_display = shares * current_price_local_currency
            gain_loss_display = current_value_display - invested_original

            gain_loss_percent = (gain_loss_display / invested_original * 100) if invested_original > 0.001 else 0

            # Update stock data with values in original currency
            stock['current_price'] = current_price_usd  # Keep USD for API compatibility
            stock['current_value'] = current_value_display  # Display in original currency
            stock['gain_loss'] = gain_loss_display  # Display in original currency
            stock['pure_gain'] = gain_loss_display  # Add pure_gain field
            stock['gain_loss_percent'] = gain_loss_percent
            stock['percent_gain'] = gain_loss_percent  # Add percent_gain field for template compatibility

            updated_count += 1
            needs_saving = True

    if needs_saving:
        session['portfolio_data'] = portfolio
        session.modified = True
        current_app.logger.info(f"Portfolio values updated for {updated_count} stocks.")
    else:
        current_app.logger.info("No portfolio values were updated in this cycle.")


def calculate_portfolio_health():
    """Calculate portfolio health score based on fundamental metrics."""
    try:
        portfolio = fetch_portfolio_data()
        if not portfolio:
            return {'error': 'No portfolio data available'}

        # Get tickers for analysis
        tickers = [stock.get('ticker') for stock in portfolio if stock.get('ticker') and stock.get('ticker') != 'UNKNOWN']
        
        if not tickers:
            return {'error': 'No valid tickers in portfolio'}

        # Fetch fundamentals for all tickers in parallel
        def fetch_fundamentals_task(ticker):
            return ticker, fetch_eodhd_fundamentals(ticker)

        fundamentals_cache = {}
        try:
            results = executor.map(fetch_fundamentals_task, tickers)
            for ticker, fundamentals in results:
                fundamentals_cache[ticker] = fundamentals
        except Exception as e:
            current_app.logger.error(f"Error fetching fundamentals for health calculation: {e}")

        # Calculate health metrics for each stock
        health_scores = []
        total_weight = 0
        
        for stock in portfolio:
            ticker = stock.get('ticker')
            if ticker not in fundamentals_cache or not fundamentals_cache[ticker]:
                continue
                
            fundamentals = fundamentals_cache[ticker]
            weight = safe_float(stock.get('current_value', 0.0))
            
            if weight <= 0:
                continue
                
            # Calculate comprehensive metrics
            metrics = calculate_comprehensive_financial_metrics(fundamentals, ticker, include_peer_context=False)
            
            # Calculate health score based on key metrics
            score = calculate_stock_health_score(metrics)
            
            health_scores.append({
                'ticker': ticker,
                'weight': weight,
                'score': score,
                'metrics': metrics
            })
            total_weight += weight

        if not health_scores:
            return {'error': 'Unable to calculate health for any holdings'}

        # Calculate weighted average health score
        weighted_score = sum(item['score'] * item['weight'] for item in health_scores) / total_weight
        
        # Determine health status
        if weighted_score >= 80:
            status = 'Excellent'
            color = '#00d4aa'
        elif weighted_score >= 70:
            status = 'Good'
            color = '#4CAF50'
        elif weighted_score >= 60:
            status = 'Fair'
            color = '#FF9800'
        elif weighted_score >= 50:
            status = 'Poor'
            color = '#f44336'
        else:
            status = 'Very Poor'
            color = '#d32f2f'

        return {
            'overall_score': weighted_score,
            'status': status,
            'color': color,
            'individual_scores': health_scores,
            'total_holdings': len(health_scores)
        }

    except Exception as e:
        current_app.logger.error(f"Error calculating portfolio health: {e}")
        return {'error': str(e)}


def calculate_stock_health_score(metrics):
    """Calculate health score for individual stock based on financial metrics."""
    try:
        score = 0
        factors = 0
        
        # ROIC (25% weight)
        if 'efficiency' in metrics and 'roic' in metrics['efficiency']:
            roic_value = metrics['efficiency']['roic'].get('value')
            if roic_value is not None:
                if roic_value >= 0.15:  # 15%+
                    score += 25
                elif roic_value >= 0.10:  # 10-15%
                    score += 20
                elif roic_value >= 0.05:  # 5-10%
                    score += 15
                elif roic_value >= 0:  # 0-5%
                    score += 10
                else:  # Negative
                    score += 0
                factors += 1

        # P/E Ratio (20% weight)
        if 'valuation' in metrics and 'pe_ratio' in metrics['valuation']:
            pe_value = metrics['valuation']['pe_ratio'].get('value')
            if pe_value is not None and pe_value > 0:
                if pe_value <= 15:
                    score += 20
                elif pe_value <= 25:
                    score += 15
                elif pe_value <= 35:
                    score += 10
                else:
                    score += 5
                factors += 1

        # Debt-to-Equity (20% weight)
        if 'leverage' in metrics and 'debt_to_equity' in metrics['leverage']:
            de_value = metrics['leverage']['debt_to_equity'].get('value')
            if de_value is not None:
                if de_value <= 0.3:
                    score += 20
                elif de_value <= 0.6:
                    score += 15
                elif de_value <= 1.0:
                    score += 10
                else:
                    score += 5
                factors += 1

        # Current Ratio (15% weight)
        if 'liquidity' in metrics and 'current_ratio' in metrics['liquidity']:
            cr_value = metrics['liquidity']['current_ratio'].get('value')
            if cr_value is not None:
                if cr_value >= 2.0:
                    score += 15
                elif cr_value >= 1.5:
                    score += 12
                elif cr_value >= 1.0:
                    score += 8
                else:
                    score += 3
                factors += 1

        # Operating Margin (20% weight)
        if 'profitability' in metrics and 'operating_margin' in metrics['profitability']:
            om_value = metrics['profitability']['operating_margin'].get('value')
            if om_value is not None:
                if om_value >= 0.20:  # 20%+
                    score += 20
                elif om_value >= 0.15:  # 15-20%
                    score += 16
                elif om_value >= 0.10:  # 10-15%
                    score += 12
                elif om_value >= 0.05:  # 5-10%
                    score += 8
                else:  # <5%
                    score += 4
                factors += 1

        # Return average score if we have at least 3 factors
        if factors >= 3:
            return score / factors * 100 / 100  # Normalize to 0-100 scale
        else:
            return 50  # Default neutral score if insufficient data

    except Exception as e:
        current_app.logger.error(f"Error calculating stock health score: {e}")
        return 50


def add_investment(ticker, shares, price):
    """Add a new investment to the portfolio."""
    try:
        portfolio = fetch_portfolio_data()
        
        # Check if investment already exists
        existing_investment = None
        for stock in portfolio:
            if stock.get('ticker') == ticker:
                existing_investment = stock
                break
        
        if existing_investment:
            # Update existing investment
            old_shares = safe_float(existing_investment.get('shares', 0))
            old_invested = safe_float(existing_investment.get('amount_invested', 0))
            
            new_shares = old_shares + safe_float(shares)
            new_invested = old_invested + (safe_float(shares) * safe_float(price))
            
            existing_investment['shares'] = new_shares
            existing_investment['amount_invested'] = new_invested
            existing_investment['average_price'] = new_invested / new_shares if new_shares > 0 else 0
        else:
            # Add new investment - use portfolio currency from session
            portfolio_currency = session.get('portfolio_currency', 'USD')
            new_investment = {
                'ticker': ticker,
                'shares': safe_float(shares),
                'purchase_price': safe_float(price),
                'amount_invested': safe_float(shares) * safe_float(price),
                'average_price': safe_float(price),
                'currency': portfolio_currency,  # Use session portfolio currency
                'current_price': safe_float(price),  # Initialize with purchase price
                'current_value': safe_float(shares) * safe_float(price),
                'gain_loss': 0.0,
                'gain_loss_percent': 0.0
            }
            portfolio.append(new_investment)
        
        # Save updated portfolio
        session['portfolio_data'] = portfolio
        session.modified = True
        
        return {'success': True, 'message': 'Investment added successfully'}
        
    except Exception as e:
        current_app.logger.error(f"Error adding investment: {e}")
        return {'success': False, 'error': str(e)}


def update_investment(ticker, shares, price):
    """Update an existing investment in the portfolio."""
    try:
        portfolio = fetch_portfolio_data()
        
        # Find the investment to update
        investment = None
        for stock in portfolio:
            if stock.get('ticker') == ticker:
                investment = stock
                break
        
        if not investment:
            return {'success': False, 'error': 'Investment not found'}
        
        # Update investment details
        investment['shares'] = safe_float(shares)
        investment['purchase_price'] = safe_float(price)
        investment['amount_invested'] = safe_float(shares) * safe_float(price)
        investment['average_price'] = safe_float(price)
        
        # Save updated portfolio
        session['portfolio_data'] = portfolio
        session.modified = True
        
        return {'success': True, 'message': 'Investment updated successfully'}
        
    except Exception as e:
        current_app.logger.error(f"Error updating investment: {e}")
        return {'success': False, 'error': str(e)}


def delete_investment(ticker):
    """Delete an investment from the portfolio."""
    try:
        portfolio = fetch_portfolio_data()
        
        # Find and remove the investment
        updated_portfolio = [stock for stock in portfolio if stock.get('ticker') != ticker]
        
        if len(updated_portfolio) == len(portfolio):
            return {'success': False, 'error': 'Investment not found'}
        
        # Save updated portfolio
        session['portfolio_data'] = updated_portfolio
        session.modified = True
        
        return {'success': True, 'message': 'Investment deleted successfully'}
        
    except Exception as e:
        current_app.logger.error(f"Error deleting investment: {e}")
        return {'success': False, 'error': str(e)}


def reset_portfolio():
    """Reset the entire portfolio."""
    try:
        session['portfolio_data'] = []
        session['cash_position'] = 0.0
        session.modified = True
        
        return {'success': True, 'message': 'Portfolio reset successfully'}
        
    except Exception as e:
        current_app.logger.error(f"Error resetting portfolio: {e}")
        return {'success': False, 'error': str(e)}