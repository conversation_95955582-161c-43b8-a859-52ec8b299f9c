#!/usr/bin/env python3
"""
Test to verify that total portfolio percentage doesn't change when switching currencies.
"""

# CENTRALIZED CURRENCY CONVERSION RATES
CURRENCY_CONVERSION_RATES = {
    'USD': 1.0,
    'DKK': 6.9,    # 1 USD = 6.9 DKK
    'PHP': 56.0,   # 1 USD = 56 PHP
}

def convert_currency(amount, from_currency, to_currency):
    """Convert amount from one currency to another using centralized rates."""
    if from_currency == to_currency:
        return amount
    
    # Convert to USD first if not already USD
    if from_currency != 'USD':
        usd_rate = CURRENCY_CONVERSION_RATES.get(from_currency, 1.0)
        amount_usd = amount / usd_rate
    else:
        amount_usd = amount
    
    # Convert from USD to target currency
    target_rate = CURRENCY_CONVERSION_RATES.get(to_currency, 1.0)
    return amount_usd * target_rate

def safe_float(value):
    """Safely convert value to float."""
    try:
        return float(value)
    except (ValueError, TypeError):
        return 0.0

def calculate_portfolio_data_fixed(portfolio, cash_position, display_currency):
    """Fixed version of calculate_portfolio_data that preserves percentages."""
    
    # Convert all values to display currency for portfolio totals
    total_invested_display = 0
    total_current_value_display = 0
    
    # CRITICAL FIX: Calculate percentage in USD to ensure consistency across currency switches
    total_invested_usd = 0
    total_current_value_usd = 0

    for stock in portfolio:
        # Use amount_invested_currency if available, otherwise fall back to currency
        currency = stock.get('amount_invested_currency', stock.get('currency', 'USD'))
        
        # Get amounts in original currency
        invested_amount = safe_float(stock.get('amount_invested', 0.0))
        current_value = safe_float(stock.get('current_value', 0.0))
        
        # Convert to display currency for totals
        invested_display = convert_currency(invested_amount, currency, display_currency)
        current_val_display = convert_currency(current_value, currency, display_currency)

        total_invested_display += invested_display
        total_current_value_display += current_val_display
        
        # CRITICAL FIX: Also convert to USD for consistent percentage calculation
        invested_usd = convert_currency(invested_amount, currency, 'USD')
        current_val_usd = convert_currency(current_value, currency, 'USD')
        
        total_invested_usd += invested_usd
        total_current_value_usd += current_val_usd

    # Cash position should also be converted to display currency if needed
    cash_display = convert_currency(cash_position, 'USD', display_currency)
    total_portfolio_value = total_current_value_display + cash_display

    # CRITICAL FIX: Calculate percentage based on USD amounts to ensure consistency
    total_pure_gain_usd = total_current_value_usd - total_invested_usd
    total_percent_gain = (total_pure_gain_usd / total_invested_usd * 100) if total_invested_usd > 0.001 else 0
    
    # Display gain in the user's display currency
    total_pure_gain_display = total_current_value_display - total_invested_display

    summary = {
        'total_invested': total_invested_display,
        'total_current_value_stocks': total_current_value_display,
        'cash_position': cash_display,
        'total_portfolio_value': total_portfolio_value,
        'total_pure_gain': total_pure_gain_display,  # Display gain in user's currency
        'total_percent_gain': total_percent_gain,    # Percentage calculated consistently in USD
        'stock_count': len(portfolio)
    }
    return summary

def test_total_percentage_consistency():
    """Test that total portfolio percentage remains consistent across currency switches."""
    print("🧪 Testing Total Portfolio Percentage Consistency")
    print("=" * 50)
    
    # Create a test portfolio with mixed currencies
    test_portfolio = [
        {
            'ticker': 'AAPL.US',
            'amount_invested': 1000.0,  # $1000 USD
            'current_value': 1200.0,    # $1200 USD (20% gain)
            'currency': 'USD',
            'amount_invested_currency': 'USD'
        },
        {
            'ticker': 'NOVO.CO',
            'amount_invested': 6900.0,  # 6900 DKK
            'current_value': 7590.0,    # 7590 DKK (10% gain)
            'currency': 'DKK',
            'amount_invested_currency': 'DKK'
        }
    ]
    
    cash_position = 0.0  # No cash
    
    # Test in USD
    print("1. Portfolio in USD:")
    summary_usd = calculate_portfolio_data_fixed(test_portfolio, cash_position, 'USD')
    print(f"   Total Invested: ${summary_usd['total_invested']:.2f}")
    print(f"   Total Current: ${summary_usd['total_current_value_stocks']:.2f}")
    print(f"   Total Gain: ${summary_usd['total_pure_gain']:.2f}")
    print(f"   Total Percentage: {summary_usd['total_percent_gain']:.2f}%")
    
    # Test in DKK
    print("\n2. Portfolio in DKK:")
    summary_dkk = calculate_portfolio_data_fixed(test_portfolio, cash_position, 'DKK')
    print(f"   Total Invested: {summary_dkk['total_invested']:.2f} kr")
    print(f"   Total Current: {summary_dkk['total_current_value_stocks']:.2f} kr")
    print(f"   Total Gain: {summary_dkk['total_pure_gain']:.2f} kr")
    print(f"   Total Percentage: {summary_dkk['total_percent_gain']:.2f}%")
    
    # Test in PHP
    print("\n3. Portfolio in PHP:")
    summary_php = calculate_portfolio_data_fixed(test_portfolio, cash_position, 'PHP')
    print(f"   Total Invested: ₱{summary_php['total_invested']:.2f}")
    print(f"   Total Current: ₱{summary_php['total_current_value_stocks']:.2f}")
    print(f"   Total Gain: ₱{summary_php['total_pure_gain']:.2f}")
    print(f"   Total Percentage: {summary_php['total_percent_gain']:.2f}%")
    
    # Check if percentages are consistent
    usd_percent = summary_usd['total_percent_gain']
    dkk_percent = summary_dkk['total_percent_gain']
    php_percent = summary_php['total_percent_gain']
    
    print("\n" + "=" * 50)
    print("📊 PERCENTAGE CONSISTENCY CHECK:")
    print(f"   USD: {usd_percent:.2f}%")
    print(f"   DKK: {dkk_percent:.2f}%")
    print(f"   PHP: {php_percent:.2f}%")
    
    if (abs(usd_percent - dkk_percent) < 0.01 and 
        abs(usd_percent - php_percent) < 0.01 and 
        abs(dkk_percent - php_percent) < 0.01):
        print("   ✅ SUCCESS: Percentages are consistent across all currencies!")
        return True
    else:
        print("   ❌ FAILURE: Percentages are inconsistent!")
        return False

if __name__ == "__main__":
    success = test_total_percentage_consistency()
    if success:
        print("\n🎉 The fix works! Total portfolio percentage will no longer change when switching currencies.")
    else:
        print("\n⚠️  The fix needs more work.")
